const express = require('express');
const metascraper = require('metascraper')([
  require('metascraper-author')(),
  require('metascraper-date')(),
  require('metascraper-description')(),
  require('metascraper-image')(),
  require('metascraper-logo')(),
  require('metascraper-clearbit')(),
  require('metascraper-publisher')(),
  require('metascraper-title')(),
  require('metascraper-url')()
]);
const axios = require('axios');

const router = express.Router();

router.post('/', async (req, res) => {
  try {
    const { url, elements } = req.body;
    
    if (!url) {
      return res.status(400).json({ error: 'URL is required' });
    }

    // Fetch the HTML content
    const response = await axios.get(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      },
      timeout: 10000
    });

    const html = response.data;
    const metadata = await metascraper({ html, url });

    // Filter results based on requested elements
    let filteredResults = {};
    
    if (!elements || elements.length === 0) {
      // Return all available metadata if no specific elements requested
      filteredResults = metadata;
    } else {
      // Map common element names to metascraper fields
      const elementMapping = {
        'title': 'title',
        'description': 'description',
        'image': 'image',
        'images': 'image', // metascraper returns single image
        'author': 'author',
        'date': 'date',
        'publisher': 'publisher',
        'logo': 'logo',
        'url': 'url',
        'price': 'price',
        'old_price': 'old_price'
      };

      elements.forEach(element => {
        const field = elementMapping[element.toLowerCase()];
        if (field && metadata[field]) {
          filteredResults[element] = metadata[field];
        }
      });
    }

    // Add some additional extracted data
    const result = {
      url: url,
      scraper: 'metascraper',
      timestamp: new Date().toISOString(),
      data: filteredResults,
      success: true
    };

    res.json(result);

  } catch (error) {
    console.error('Metascraper error:', error);
    res.status(500).json({
      error: 'Failed to scrape URL with metascraper',
      message: error.message,
      success: false
    });
  }
});

module.exports = router;
