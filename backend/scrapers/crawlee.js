const express = require('express');
const { PlaywrightCrawler } = require('crawlee');
const { chromium } = require('playwright');

const router = express.Router();

router.post('/', async (req, res) => {
  try {
    const { url, elements } = req.body;
    
    if (!url) {
      return res.status(400).json({ error: 'URL is required' });
    }

    let scrapedData = {};

    const crawler = new PlaywrightCrawler({
      launchContext: {
        launchOptions: {
          headless: true,
        },
      },
      async requestHandler({ page, request }) {
        try {
          // Wait for the page to load
          await page.waitForLoadState('networkidle');

          // Extract data based on requested elements
          const data = await page.evaluate((requestedElements) => {
            const result = {};

            // Helper function to get text content safely
            const getTextContent = (selector) => {
              const element = document.querySelector(selector);
              return element ? element.textContent.trim() : null;
            };

            // Helper function to get attribute safely
            const getAttribute = (selector, attribute) => {
              const element = document.querySelector(selector);
              return element ? element.getAttribute(attribute) : null;
            };

            // Helper function to get all matching elements
            const getAllElements = (selector, attribute = null) => {
              const elements = document.querySelectorAll(selector);
              return Array.from(elements).map(el => 
                attribute ? el.getAttribute(attribute) : el.textContent.trim()
              ).filter(item => item);
            };

            // Extract title
            if (!requestedElements || requestedElements.includes('title')) {
              result.title = getTextContent('title') || 
                           getTextContent('h1') || 
                           getTextContent('[data-testid="title"]') ||
                           getTextContent('.title');
            }

            // Extract description
            if (!requestedElements || requestedElements.includes('description')) {
              result.description = getAttribute('meta[name="description"]', 'content') ||
                                 getAttribute('meta[property="og:description"]', 'content') ||
                                 getTextContent('.description') ||
                                 getTextContent('[data-testid="description"]');
            }

            // Extract images
            if (!requestedElements || requestedElements.includes('images') || requestedElements.includes('image')) {
              const images = [];
              
              // Get og:image
              const ogImage = getAttribute('meta[property="og:image"]', 'content');
              if (ogImage) images.push(ogImage);
              
              // Get all img src attributes
              const imgSrcs = getAllElements('img', 'src');
              images.push(...imgSrcs);
              
              result.images = [...new Set(images)]; // Remove duplicates
            }

            // Extract prices (common selectors)
            if (!requestedElements || requestedElements.includes('price')) {
              const priceSelectors = [
                '.price', '[data-testid="price"]', '.cost', '.amount',
                '[class*="price"]', '[id*="price"]', '.currency'
              ];

              for (const selector of priceSelectors) {
                const price = getTextContent(selector);
                if (price && /[\d.,]+/.test(price)) {
                  result.price = price;
                  break;
                }
              }
            }

            // Extract old prices
            if (!requestedElements || requestedElements.includes('old_price')) {
              const oldPriceSelectors = [
                '.old-price', '.original-price', '.was-price', '.regular-price',
                '.price-old', '.price-before', '.crossed-price', '.strikethrough-price',
                '[data-testid="old-price"]', '[class*="old-price"]', '[class*="original-price"]',
                '[class*="was-price"]', 'del', 's', '.line-through', '.price-strike'
              ];

              for (const selector of oldPriceSelectors) {
                const oldPrice = getTextContent(selector);
                if (oldPrice && /[\d.,]+/.test(oldPrice)) {
                  result.old_price = oldPrice;
                  break;
                }
              }
            }

            // Extract author
            if (!requestedElements || requestedElements.includes('author')) {
              result.author = getAttribute('meta[name="author"]', 'content') ||
                            getTextContent('.author') ||
                            getTextContent('[data-testid="author"]') ||
                            getTextContent('.byline');
            }

            return result;
          }, elements);

          scrapedData = data;

        } catch (error) {
          console.error('Page evaluation error:', error);
          throw error;
        }
      },
    });

    // Add the URL to the crawler queue
    await crawler.addRequests([url]);
    
    // Run the crawler
    await crawler.run();

    const result = {
      url: url,
      scraper: 'crawlee',
      timestamp: new Date().toISOString(),
      data: scrapedData,
      success: true
    };

    res.json(result);

  } catch (error) {
    console.error('Crawlee error:', error);
    res.status(500).json({
      error: 'Failed to scrape URL with Crawlee',
      message: error.message,
      success: false
    });
  }
});

module.exports = router;
