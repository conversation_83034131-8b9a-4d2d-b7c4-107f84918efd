const express = require('express');
const { chromium } = require('playwright');
const UserAgent = require('user-agents');

const router = express.Router();

// Realistic browser configurations
const getBrowserConfig = () => {
  const userAgent = new UserAgent();
  
  return {
    headless: true, // Verwende headless für bessere Performance
    args: [
      '--no-sandbox',
      '--disable-setuid-sandbox',
      '--disable-dev-shm-usage',
      '--disable-accelerated-2d-canvas',
      '--no-first-run',
      '--no-zygote',
      '--disable-gpu',
      '--disable-features=VizDisplayCompositor',
      '--disable-background-timer-throttling',
      '--disable-backgrounding-occluded-windows',
      '--disable-renderer-backgrounding',
      '--disable-field-trial-config',
      '--disable-back-forward-cache',
      '--disable-backgrounding-occluded-windows',
      '--disable-features=TranslateUI',
      '--disable-ipc-flooding-protection',
      '--window-size=1920,1080',
      '--start-maximized'
    ],
    viewport: { width: 1920, height: 1080 },
    userAgent: userAgent.toString(),
    locale: 'de-DE',
    timezoneId: 'Europe/Berlin'
  };
};

// Simulate human behavior
const humanDelay = (min = 1000, max = 3000) => {
  return new Promise(resolve => {
    const delay = Math.floor(Math.random() * (max - min + 1)) + min;
    setTimeout(resolve, delay);
  });
};

// Random mouse movements
const simulateHumanBehavior = async (page) => {
  // Random mouse movements
  for (let i = 0; i < 3; i++) {
    await page.mouse.move(
      Math.random() * 1920,
      Math.random() * 1080,
      { steps: 10 }
    );
    await humanDelay(100, 500);
  }
  
  // Random scroll
  await page.evaluate(() => {
    window.scrollTo(0, Math.random() * 500);
  });
  
  await humanDelay(500, 1500);
};

// Enhanced page stealth setup
const setupPageStealth = async (page) => {
  // Override webdriver detection
  await page.addInitScript(() => {
    Object.defineProperty(navigator, 'webdriver', {
      get: () => undefined,
    });
    
    // Remove automation indicators
    delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
    delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
    delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;
    
    // Override permissions
    const originalQuery = window.navigator.permissions.query;
    window.navigator.permissions.query = (parameters) => (
      parameters.name === 'notifications' ?
        Promise.resolve({ state: Notification.permission }) :
        originalQuery(parameters)
    );
    
    // Override plugins
    Object.defineProperty(navigator, 'plugins', {
      get: () => [1, 2, 3, 4, 5],
    });
    
    // Override languages
    Object.defineProperty(navigator, 'languages', {
      get: () => ['de-DE', 'de', 'en-US', 'en'],
    });
  });
  
  // Set realistic headers
  await page.setExtraHTTPHeaders({
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'Accept-Encoding': 'gzip, deflate, br',
    'Accept-Language': 'de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7',
    'Cache-Control': 'max-age=0',
    'sec-ch-ua': '"Google Chrome";v="119", "Chromium";v="119", "Not?A_Brand";v="24"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"macOS"',
    'Sec-Fetch-Dest': 'document',
    'Sec-Fetch-Mode': 'navigate',
    'Sec-Fetch-Site': 'none',
    'Sec-Fetch-User': '?1',
    'Upgrade-Insecure-Requests': '1'
  });
};

router.post('/', async (req, res) => {
  let browser = null;
  let page = null;
  
  try {
    const { url, elements, waitTime = 5000, retries = 3 } = req.body;
    
    if (!url) {
      return res.status(400).json({ error: 'URL is required' });
    }

    console.log(`🚀 Starting stealth scraping for: ${url}`);
    
    // Launch browser with stealth configuration
    const config = getBrowserConfig();
    browser = await chromium.launch(config);
    
    const context = await browser.newContext({
      viewport: config.viewport,
      userAgent: config.userAgent,
      locale: config.locale,
      timezoneId: config.timezoneId,
      permissions: ['geolocation'],
      geolocation: { latitude: 52.520008, longitude: 13.404954 }, // Berlin
    });
    
    page = await context.newPage();
    
    // Setup stealth measures
    await setupPageStealth(page);
    
    // Navigate with retry logic
    let attempt = 0;
    let navigationSuccess = false;
    
    while (attempt < retries && !navigationSuccess) {
      try {
        console.log(`📡 Attempt ${attempt + 1} to load page...`);
        
        // Add random delay before navigation
        await humanDelay(1000, 3000);
        
        // Navigate to page
        const response = await page.goto(url, {
          waitUntil: 'domcontentloaded',
          timeout: 30000
        });
        
        if (response && response.status() === 200) {
          navigationSuccess = true;
          console.log('✅ Page loaded successfully');
        } else {
          console.log(`⚠️ Received status: ${response ? response.status() : 'unknown'}`);
        }
        
      } catch (error) {
        console.log(`❌ Navigation attempt ${attempt + 1} failed:`, error.message);
        attempt++;
        
        if (attempt < retries) {
          await humanDelay(2000, 5000); // Wait before retry
        }
      }
    }
    
    if (!navigationSuccess) {
      throw new Error('Failed to load page after all retries');
    }
    
    // Wait for page to fully load and simulate human behavior
    await humanDelay(2000, 4000);
    await simulateHumanBehavior(page);
    
    // Check if we're on a Cloudflare challenge page
    const isChallengePage = await page.evaluate(() => {
      return document.title.includes('Just a moment') || 
             document.body.textContent.includes('Checking your browser') ||
             document.querySelector('#challenge-error-text') !== null;
    });
    
    if (isChallengePage) {
      console.log('🛡️ Cloudflare challenge detected, waiting...');
      
      // Wait for challenge to complete (up to 30 seconds)
      try {
        await page.waitForFunction(() => {
          return !document.title.includes('Just a moment') && 
                 !document.body.textContent.includes('Checking your browser');
        }, { timeout: 30000 });
        
        console.log('✅ Challenge completed');
        await humanDelay(2000, 4000);
        
      } catch (error) {
        console.log('⚠️ Challenge timeout, proceeding anyway...');
      }
    }
    
    // Additional wait time for dynamic content
    await page.waitForTimeout(waitTime);
    
    // Extract data based on requested elements
    const scrapedData = await page.evaluate((requestedElements) => {
      const result = {};
      
      // Helper functions
      const getTextContent = (selector) => {
        const element = document.querySelector(selector);
        return element ? element.textContent.trim() : null;
      };
      
      const getAttribute = (selector, attribute) => {
        const element = document.querySelector(selector);
        return element ? element.getAttribute(attribute) : null;
      };
      
      const getAllElements = (selector, attribute = null) => {
        const elements = document.querySelectorAll(selector);
        return Array.from(elements).map(el => 
          attribute ? el.getAttribute(attribute) : el.textContent.trim()
        ).filter(item => item);
      };
      
      // Extract title
      if (!requestedElements || requestedElements.includes('title')) {
        result.title = getTextContent('title') || 
                     getTextContent('h1') || 
                     getTextContent('[data-testid="title"]') ||
                     getTextContent('.title') ||
                     getTextContent('.product-title');
      }
      
      // Extract description
      if (!requestedElements || requestedElements.includes('description')) {
        result.description = getAttribute('meta[name="description"]', 'content') ||
                           getAttribute('meta[property="og:description"]', 'content') ||
                           getTextContent('.description') ||
                           getTextContent('[data-testid="description"]') ||
                           getTextContent('.product-description');
      }
      
      // Extract images
      if (!requestedElements || requestedElements.includes('images') || requestedElements.includes('image')) {
        const images = [];
        
        // Get og:image
        const ogImage = getAttribute('meta[property="og:image"]', 'content');
        if (ogImage) images.push(ogImage);
        
        // Get product images
        const productImages = getAllElements('img[src*="product"], .product-image img, [data-testid*="image"] img', 'src');
        images.push(...productImages);
        
        // Get all img src attributes as fallback
        const allImages = getAllElements('img', 'src');
        images.push(...allImages.filter(src => 
          src.includes('product') || src.includes('item') || src.includes('image')
        ));
        
        result.images = [...new Set(images)].slice(0, 10); // Limit to 10 images
      }
      
      // Extract prices with enhanced selectors
      if (!requestedElements || requestedElements.includes('price')) {
        const priceSelectors = [
          '.price', '[data-testid="price"]', '.cost', '.amount',
          '[class*="price"]', '[id*="price"]', '.currency',
          '.product-price', '.price-current', '.price-now',
          '[data-price]', '.price-value', '.sale-price'
        ];
        
        for (const selector of priceSelectors) {
          const priceElement = document.querySelector(selector);
          if (priceElement) {
            const priceText = priceElement.textContent.trim();
            if (priceText && /[\d.,]+/.test(priceText)) {
              result.price = priceText;
              break;
            }
          }
        }
        
        // Try data attributes
        if (!result.price) {
          const priceElement = document.querySelector('[data-price]');
          if (priceElement) {
            result.price = priceElement.getAttribute('data-price');
          }
        }
      }
      
      // Extract author/brand
      if (!requestedElements || requestedElements.includes('author')) {
        result.author = getAttribute('meta[name="author"]', 'content') ||
                      getTextContent('.author') ||
                      getTextContent('[data-testid="author"]') ||
                      getTextContent('.brand') ||
                      getTextContent('.manufacturer');
      }
      
      return result;
    }, elements);
    
    // Take screenshot for debugging
    await page.screenshot({ path: 'debug-screenshot.png', fullPage: false });
    
    const result = {
      url: url,
      scraper: 'stealth-playwright',
      timestamp: new Date().toISOString(),
      data: scrapedData,
      success: true,
      challengeDetected: isChallengePage
    };
    
    console.log('✅ Scraping completed successfully');
    res.json(result);
    
  } catch (error) {
    console.error('❌ Stealth Playwright error:', error);
    res.status(500).json({
      error: 'Failed to scrape URL with Stealth Playwright',
      message: error.message,
      success: false
    });
  } finally {
    if (page) await page.close();
    if (browser) await browser.close();
  }
});

module.exports = router;
