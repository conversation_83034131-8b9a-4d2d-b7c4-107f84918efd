import { useState } from 'react'

// Simple JSON Display Component
const JsonDisplay = ({ data, level = 0 }) => {
  const [collapsed, setCollapsed] = useState({})

  const toggleCollapse = (key) => {
    setCollapsed(prev => ({ ...prev, [key]: !prev[key] }))
  }

  const renderValue = (value, key, currentLevel) => {
    if (value === null) return <span className="json-null">null</span>
    if (typeof value === 'boolean') return <span className="json-boolean">{String(value)}</span>
    if (typeof value === 'number') return <span className="json-number">{value}</span>
    if (typeof value === 'string') {
      if (value.startsWith('http')) {
        return <a href={value} target="_blank" rel="noopener noreferrer" className="json-link">{value}</a>
      }
      return <span className="json-string">"{value}"</span>
    }
    if (Array.isArray(value)) {
      const isCollapsed = collapsed[key]
      return (
        <div className="json-array">
          <span
            className="json-toggle"
            onClick={() => toggleCollapse(key)}
          >
            {isCollapsed ? '▶' : '▼'} [{value.length} items]
          </span>
          {!isCollapsed && (
            <div className="json-content" style={{ marginLeft: '20px' }}>
              {value.map((item, index) => (
                <div key={index} className="json-item">
                  <span className="json-key">{index}:</span> {renderValue(item, `${key}-${index}`, currentLevel + 1)}
                </div>
              ))}
            </div>
          )}
        </div>
      )
    }
    if (typeof value === 'object') {
      const isCollapsed = collapsed[key]
      const keys = Object.keys(value)
      return (
        <div className="json-object">
          <span
            className="json-toggle"
            onClick={() => toggleCollapse(key)}
          >
            {isCollapsed ? '▶' : '▼'} {`{${keys.length} keys}`}
          </span>
          {!isCollapsed && (
            <div className="json-content" style={{ marginLeft: '20px' }}>
              {keys.map(objKey => (
                <div key={objKey} className="json-item">
                  <span className="json-key">"{objKey}":</span> {renderValue(value[objKey], `${key}-${objKey}`, currentLevel + 1)}
                </div>
              ))}
            </div>
          )}
        </div>
      )
    }
    return String(value)
  }

  return (
    <div className="json-display">
      {Object.entries(data).map(([key, value]) => (
        <div key={key} className="json-item">
          <span className="json-key">"{key}":</span> {renderValue(value, key, level)}
        </div>
      ))}
    </div>
  )
}

const ResultDisplay = ({ result }) => {
  const [viewMode, setViewMode] = useState('formatted')

  const downloadJson = () => {
    const dataStr = JSON.stringify(result, null, 2)
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr)
    
    const exportFileDefaultName = `scraped-data-${new Date().toISOString().split('T')[0]}.json`
    
    const linkElement = document.createElement('a')
    linkElement.setAttribute('href', dataUri)
    linkElement.setAttribute('download', exportFileDefaultName)
    linkElement.click()
  }

  const copyToClipboard = () => {
    navigator.clipboard.writeText(JSON.stringify(result, null, 2))
      .then(() => alert('JSON copied to clipboard!'))
      .catch(err => console.error('Failed to copy: ', err))
  }

  if (!result) return null

  return (
    <div className="result-display">
      <div className="result-header">
        <h2>Scraping Results</h2>
        <div className="result-actions">
          <div className="view-toggle">
            <button 
              className={viewMode === 'formatted' ? 'active' : ''}
              onClick={() => setViewMode('formatted')}
            >
              Formatted
            </button>
            <button 
              className={viewMode === 'raw' ? 'active' : ''}
              onClick={() => setViewMode('raw')}
            >
              Raw JSON
            </button>
          </div>
          <button onClick={copyToClipboard} className="action-btn">
            📋 Copy
          </button>
          <button onClick={downloadJson} className="action-btn">
            💾 Download
          </button>
        </div>
      </div>

      <div className="result-content">
        {result.success === false ? (
          <div className="error-result">
            <h3>❌ Scraping Failed</h3>
            <p><strong>Error:</strong> {result.error}</p>
            {result.message && <p><strong>Details:</strong> {result.message}</p>}
          </div>
        ) : (
          <>
            <div className="result-meta">
              <div className="meta-item">
                <strong>URL:</strong> {result.url}
              </div>
              <div className="meta-item">
                <strong>Scraper:</strong> {result.scraper}
              </div>
              <div className="meta-item">
                <strong>Timestamp:</strong> {new Date(result.timestamp).toLocaleString()}
              </div>
            </div>

            {viewMode === 'formatted' ? (
              <div className="formatted-result">
                <JsonDisplay data={result.data} />
              </div>
            ) : (
              <pre className="raw-json">
                {JSON.stringify(result, null, 2)}
              </pre>
            )}

            {result.data && Object.keys(result.data).length > 0 && (
              <div className="data-summary">
                <h3>📊 Data Summary</h3>
                <ul>
                  {Object.entries(result.data).map(([key, value]) => (
                    <li key={key}>
                      <strong>{key}:</strong> 
                      {Array.isArray(value) ? (
                        <span> {value.length} items</span>
                      ) : typeof value === 'string' ? (
                        <span> "{value.length > 50 ? value.substring(0, 50) + '...' : value}"</span>
                      ) : (
                        <span> {String(value)}</span>
                      )}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  )
}

export default ResultDisplay
